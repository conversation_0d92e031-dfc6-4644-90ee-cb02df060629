<View>
  <Style>
  .twoCol { display: grid; grid-template-columns: 1fr 1fr; column-gap: 2em; }
  .fullHeight { height: 100%; overflow: auto; }
  .grid4 { display: grid; grid-template-columns: auto 2fr auto 3fr; column-gap:1em; row-gap:1em; }
  .adcContainer { background-color: #e8f5e9; padding: 15px; border-radius: 8px; margin-bottom: 15px; }
  .modelContainer { background-color: #e3f2fd; padding: 15px; border-radius: 8px; margin-bottom: 15px; }
  .endpointContainer { background-color: #fff3e0; padding: 15px; border-radius: 8px; margin-bottom: 15px; }
       /* 1. Global Ant Design typography */
    .ant-typography {
      font-size: 12px !important;
      line-height: 1.3 !important;
    }

    /* 2. Headings */
    h1, h2, h3, h4 {
      font-size: 1rem !important;       /* equivalent to 16px */
      line-height: 1.3 !important;
      margin-block-start: 0.5em !important;
      margin-block-end: 0.5em !important;
    }

    /* 3. Tailwind-derived utility classes */
    .text-2xl { font-size: 1.25rem !important; }   /* ~20px */
    .text-xl  { font-size: 1.125rem !important; }  /* ~18px */
    .text-lg  { font-size: 1rem !important; }      /* 16px */
    .text-lsLabelMedium { font-size: 14px !important; }
    .text-sm { font-size: 0.75rem !important; }     /* 12px */
    .text-xs { font-size: 0.65rem !important; }     /* ~10px */

    /* 4. Annotation span labels (leaf taxonomy items) */
    .lsf-control-label,
    .lsf-control-label > span {
      font-size: 11px !important;
      line-height: 1.2 !important;
    }

    /* 5. Buttons */
    .ant-btn {
      font-size: 10px !important;
      line-height: 1.2 !important;
      padding: 4px 8px !important;
    }

    /* 6. Inputs and editable fields */
    .ant-input,
    .ant-input-affix-wrapper,
    .ant-input.is-search,
    textarea.ant-input,
    .ant-select-selection-search-input,
    [contenteditable="true"] {
      font-size: 11px !important;
      line-height: 1.3 !important;
      height: auto !important;
      padding: 4px 8px !important;
    }

    /* 7. Input placeholder text */
    .ant-input::placeholder,
    .ant-select-selection-placeholder {
      font-size: 11px !important;
      line-height: 1.3 !important;
    }

    /* 8. Dropdown selections */
    .ant-select-selection-item,
    .ant-select-selection-placeholder {
      font-size: 11px !important;
      line-height: 1.2 !important;
    }

    /* 9. Tooltips and popovers */
    .ant-tooltip-inner,
    .ant-popover-inner-content {
      font-size: 10px !important;
      line-height: 1.2 !important;
    }

    /* 10. Code blocks */
    code, pre {
      font-size: 10px !important;
      line-height: 1.2 !important;
    }

    /* 11. Fallback for any remaining text elements */
    p, span, label, li, td, th {
      font-size: 11px !important;
      line-height: 1.3 !important;
    }
 
  
  </Style>

  <View className="twoCol fullHeight">
    <View>
          <Taxonomy name="label" toName="text" labeling="true"  placeholder="Select citation spans" leafsOnly='true'>
      <Choice value="ADC" selectable="false">
        <Choice value="adc_name" color="#1abc9c"/>
        <Choice value="antibody_name" color="#2ecc71"/>
        <Choice value="clonality" color="#3498db"/>
        <Choice value="species" color="#9b59b6"/>
        <Choice value="isotype" color="#e74c3c"/>
        <Choice value="payload_name" color="#f1c40f"/>
        <Choice value="payload_target" color="#e67e22"/>
        <Choice value="linker_name" color="#d35400"/>
        <Choice value="linker_type" color="#c0392b"/>
        <Choice value="antigen_name" color="#7f8c8d"/>
      </Choice>
      <!-- Model citations -->
      <Choice value="Model">
        <Choice value="model_description" color="#16a085"/>
        <Choice value="model_type" color="#27ae60"/>
        <Choice value="cancer_type" color="#2980b9"/>
        <Choice value="cancer_subtype" color="#8e44ad"/>
      </Choice>
      <!-- Endpoint citations -->
      <Choice value="Endpoints">
        <Choice value="endpoint_type" color="#2c3e50"/>
        <Choice value="endpoint_name" color="#e84393"/>
        <Choice value="endpoint_quantitative_value" color="#fd79a8"/>
        <Choice value="endpoint_qualitative_value" color="#e17055"/>
        <Choice value="endpoint_timepoint" color="#00b894"/>
        <Choice value="endpoint_concentration" color="#0984e3"/>
      </Choice>
    </Taxonomy>
      <HyperText
        name="text"
        value="$html_url"
        valueType="url"
        inline="true"/>
      </View>
     <View>
  <View className="adcContainer">
    <Header value="ADC Attributes:"/>
    <View style="display: grid; grid-template-columns: 10fr 10fr 20fr; column-gap: 1em">
        <Header value="adc_name"/>
        <TextArea name="adc_name" toName="text" value="$adc_name" maxSubmissions="1"/>
        <TextArea name="adc_name_feedback" toName="text" rows="2" required="true" showSubmitButton="false" maxSubmissions="1" placeholder="Enter feedback"/>
    </View>
    <View style="display: grid; grid-template-columns: 10fr 10fr 20fr; column-gap: 1em">
        <Header value="antibody_name"/>
        <TextArea name="antibody_name" toName="text" value="$antibody_name" maxSubmissions="1"/>
        <TextArea name="antibody_name_feedback" toName="text" rows="2" required="true" showSubmitButton="false" maxSubmissions="1" placeholder="Enter feedback"/>
    </View>
    <View style="display: grid; grid-template-columns: 10fr 10fr 20fr; column-gap: 1em">
        <Header value="clonality"/>
        <TextArea name="clonality" toName="text" value="$clonality" maxSubmissions="1"/>
        <TextArea name="clonality_feedback" toName="text" rows="2" required="true" showSubmitButton="false" maxSubmissions="1" placeholder="Enter feedback"/>
    </View>
    <View style="display: grid; grid-template-columns: 10fr 10fr 20fr; column-gap: 1em">
        <Header value="species"/>
        <TextArea name="species" toName="text" value="$species" maxSubmissions="1"/>
        <TextArea name="species_feedback" toName="text" rows="2" required="true" showSubmitButton="false" maxSubmissions="1" placeholder="Enter feedback"/>
    </View>
    <View style="display: grid; grid-template-columns: 10fr 10fr 20fr; column-gap: 1em">
        <Header value="isotype"/>
        <TextArea name="isotype" toName="text" value="$isotype" maxSubmissions="1"/>
        <TextArea name="isotype_feedback" toName="text" rows="2" required="true" showSubmitButton="false" maxSubmissions="1" placeholder="Enter feedback"/>
    </View>
    <View style="display: grid; grid-template-columns: 10fr 10fr 20fr; column-gap: 1em">
        <Header value="payload_name"/>
        <TextArea name="payload_name" toName="text" value="$payload_name" maxSubmissions="1"/>
        <TextArea name="payload_name_feedback" toName="text" rows="2" required="true" showSubmitButton="false" maxSubmissions="1" placeholder="Enter feedback"/>
    </View>
    <View style="display: grid; grid-template-columns: 10fr 10fr 20fr; column-gap: 1em">
        <Header value="payload_target"/>
        <TextArea name="payload_target" toName="text" value="$payload_target" maxSubmissions="1"/>
        <TextArea name="payload_target_feedback" toName="text" rows="2" required="true" showSubmitButton="false" maxSubmissions="1" placeholder="Enter feedback"/>
    </View>
    <View style="display: grid; grid-template-columns: 10fr 10fr 20fr; column-gap: 1em">
        <Header value="linker_name"/>
        <TextArea name="linker_name" toName="text" value="$linker_name" maxSubmissions="1"/>
        <TextArea name="linker_name_feedback" toName="text" rows="2" required="true" showSubmitButton="false" maxSubmissions="1" placeholder="Enter feedback"/>
    </View>
    <View style="display: grid; grid-template-columns: 10fr 10fr 20fr; column-gap: 1em">
        <Header value="linker_type"/>
        <TextArea name="linker_type" toName="text" value="$linker_type" maxSubmissions="1"/>
        <TextArea name="linker_type_feedback" toName="text" rows="2" required="true" showSubmitButton="false" maxSubmissions="1" placeholder="Enter feedback"/>
    </View>
    <View style="display: grid; grid-template-columns: 10fr 10fr 20fr; column-gap: 1em">
        <Header value="antigen_name"/>
        <TextArea name="antigen_name" toName="text" value="$antigen_name" maxSubmissions="1"/>
        <TextArea name="antigen_name_feedback" toName="text" rows="2" required="true" showSubmitButton="false" maxSubmissions="1" placeholder="Enter feedback"/>
    </View>
  </View>
  
  <View className="modelContainer">
    <Header value="Model Attributes:"/>
    <View style="display: grid; grid-template-columns: 10fr 10fr 20fr; column-gap: 1em">
        <Header value="model_description"/>
        <TextArea name="model_description" toName="text" value="$model_description" maxSubmissions="1"/>
        <TextArea name="model_description_feedback" toName="text" rows="2" required="true" showSubmitButton="false" maxSubmissions="1" placeholder="Enter feedback"/>
    </View>
    <View style="display: grid; grid-template-columns: 10fr 10fr 20fr; column-gap: 1em">
        <Header value="model_type"/>
        <TextArea name="model_type" toName="text" value="$model_type" maxSubmissions="1"/>
        <TextArea name="model_type_feedback" toName="text" rows="2" required="true" showSubmitButton="false" maxSubmissions="1" placeholder="Enter feedback"/>
    </View>
    <View style="display: grid; grid-template-columns: 10fr 10fr 20fr; column-gap: 1em">
        <Header value="cancer_type"/>
        <TextArea name="cancer_type" toName="text" value="$cancer_type" maxSubmissions="1"/>
        <TextArea name="cancer_type_feedback" toName="text" rows="2" required="true" showSubmitButton="false" maxSubmissions="1" placeholder="Enter feedback"/>
    </View>
    <View style="display: grid; grid-template-columns: 10fr 10fr 20fr; column-gap: 1em">
        <Header value="cancer_subtype"/>
        <TextArea name="cancer_subtype" toName="text" value="$cancer_subtype" maxSubmissions="1"/>
        <TextArea name="cancer_subtype_feedback" toName="text" rows="2" required="true" showSubmitButton="false" maxSubmissions="1" placeholder="Enter feedback"/>
    </View>
  </View>
  
  <View className="endpointContainer">
    <Header value="Endpoint Attributes:"/>
    <View style="display: grid; grid-template-columns: 10fr 10fr 20fr; column-gap: 1em">
        <Header value="endpoint_type"/>
        <TextArea name="endpoint_type" toName="text" value="$endpoint_type" maxSubmissions="1"/>
        <TextArea name="endpoint_type_feedback" toName="text" rows="2" required="true" showSubmitButton="false" maxSubmissions="1" placeholder="Enter feedback"/>
    </View>
    <View style="display: grid; grid-template-columns: 10fr 10fr 20fr; column-gap: 1em">
        <Header value="endpoint_name"/>
        <TextArea name="endpoint_name" toName="text" value="$endpoint_name" maxSubmissions="1"/>
        <TextArea name="endpoint_name_feedback" toName="text" rows="2" required="true" showSubmitButton="false" maxSubmissions="1" placeholder="Enter feedback"/>
    </View>
    <View style="display: grid; grid-template-columns: 10fr 10fr 20fr; column-gap: 1em">
        <Header value="endpoint_quantitative_value"/>
        <TextArea name="endpoint_quantitative_value" toName="text" value="$endpoint_quantitative_value" maxSubmissions="1"/>
        <TextArea name="endpoint_quantitative_value_feedback" toName="text" rows="2" required="true" showSubmitButton="false" maxSubmissions="1" placeholder="Enter feedback"/>
    </View>
    <View style="display: grid; grid-template-columns: 10fr 10fr 20fr; column-gap: 1em">
        <Header value="endpoint_qualitative_value"/>
        <TextArea name="endpoint_qualitative_value" toName="text" value="$endpoint_qualitative_value" maxSubmissions="1"/>
        <TextArea name="endpoint_qualitative_value_feedback" toName="text" rows="2" required="true" showSubmitButton="false" maxSubmissions="1" placeholder="Enter feedback"/>
    </View>
    <View style="display: grid; grid-template-columns: 10fr 10fr 20fr; column-gap: 1em">
        <Header value="endpoint_timepoint"/>
        <TextArea name="endpoint_timepoint" toName="text" value="$endpoint_timepoint" maxSubmissions="1"/>
        <TextArea name="endpoint_timepoint_feedback" toName="text" rows="2" required="true" showSubmitButton="false" maxSubmissions="1" placeholder="Enter feedback"/>
    </View>
    <View style="display: grid; grid-template-columns: 10fr 10fr 20fr; column-gap: 1em">
        <Header value="endpoint_concentration"/>
        <TextArea name="endpoint_concentration" toName="text" value="$endpoint_concentration" maxSubmissions="1"/>
        <TextArea name="endpoint_concentration_feedback" toName="text" rows="2" required="true" showSubmitButton="false" maxSubmissions="1" placeholder="Enter feedback"/>
    </View>
  </View>
</View>
</View>
</View>
